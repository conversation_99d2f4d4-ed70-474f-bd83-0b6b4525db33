#!/usr/bin/env python3
"""
参考任务决策Prompt

基于参考成功案例的UI自动化测试决策prompt
参考decision_definition_prompt.py的编写风格和描述方式

Prompt块引用说明：
- get_reference_role_definition(): 角色定位 - 定义Agent的核心职责
- get_reference_test_case_description(): 测试用例信息 - 提供当前测试用例的详细信息
- _build_reference_actions_text(): 成功案例 - 提供参考的成功执行历史
- _build_current_execution_history(): 执行记忆 - 当前任务的执行记录
- get_image_element_extractor_prompt(): 图片元素提取器 - 指导如何从截图中提取相关元素
- get_interface_analysis_prompt(): 界面分析 - 指导如何分析当前界面
- get_reference_action_list(): 动作列表 - 可执行的动作类型和格式
- get_reference_learning_strategy(): 执行策略 - 如何学习和复用成功案例
- get_reference_exception_handling(): 异常场景 - 处理各种异常情况的策略
- get_reference_action_decision_prompt(): 动作决策 - 决策流程和步骤
- get_reference_output_example(): 输出格式 - JSON输出格式要求
- get_reference_output_requirement(): 输出要求 - 输出的具体要求
- get_reference_execution_invoke_prompt(): 执行流程 - 整体执行流程指导
- get_reference_user_invoke_prompt(): 用户调用提示 - 最终的执行和输出要求
"""

import json
import re
from src.domain.reference_task.repo.reference_state import ReferenceTaskState


def build_reference_decision_prompt(state: ReferenceTaskState) -> str:
    """
    构建参考任务决策prompt

    Args:
        state: 参考任务状态

    Returns:
        完整的系统prompt
    """

    # 构建参考任务信息和当前执行历史
    reference_actions_text = _build_reference_actions_text(state["reference_actions"])
    current_execution_history = _build_current_execution_history(state.get("history", []))

    prompt = f"""
########## 角色定位 ##########
{get_reference_role_definition()}

########## 测试用例信息 ##########
{get_reference_test_case_description(state)}

########## 成功案例 ##########
{reference_actions_text}

########## 执行记忆 ##########
{current_execution_history}

########## 界面分析 ##########
{get_interface_analysis_prompt()}

########## 动作列表 ##########
{get_reference_action_list()}

########## 异常场景 ##########
{get_reference_exception_handling()}

########## 动作决策 ##########
{get_simplified_action_decision_prompt()}

########## 输出格式 ##########
{get_reference_output_example()}

########## 输出要求 ##########
{get_reference_output_requirement()}
"""

    return prompt


def get_reference_role_definition() -> str:
    """获取参考任务的角色定义"""
    return """你是一个专业的安卓软件UI自动化测试Agent，你的任务是按照<成功案例>的操作步骤进行回归测试
**核心职责**：
- 严格按照<成功案例>的操作路径逐步执行，并在执行前后进行结果验证  
- 直接复用<成功案例>中的决策和动作，并根据界面实际情况调整坐标
- 重点关注并处理界面变化和异常情况，确保按照<成功案例>执行路径执行  
- 当界面与<成功案例>描述不一致时，参考<成功案例>中的导航路径，并读取<执行记忆>，自主决策回到正确界面  
**切换到下一个执行步骤时，必须进行结果验证：如果当前界面内容与下一个步骤的“界面分析”(预期结果)不一致，则不能进入下一步，必须重新执行当前步骤或进行异常处理后再验证**"""


def get_reference_test_case_description(state: ReferenceTaskState) -> str:
    """获取参考任务的测试用例描述"""
    # 优先使用实际的测试用例名称，避免显示参考任务ID
    test_case_name = state.get("test_case_name", "未知测试用例")
    test_case_description = state.get("test_case_description", state.get("task", ""))
    expected_result = state.get("expected_result", "")
    content = f"""- **用例名称**: {test_case_name}
- **用例步骤**:
{test_case_description}
- **期望结果**: {expected_result}"""
    return content


def get_image_element_extractor_prompt() -> str:
    return """
**快速分析<当前轮界面截图>，重点关注：**
  - 对比<成功案例>中对应步骤的目标元素是否可见
  - 识别可能影响操作的异常元素（弹窗、遮挡等）
  - 确认界面状态是否与成功案例一致"""


def get_interface_analysis_prompt() -> str:
    """获取界面分析提示"""
    return """
**快速分析<当前轮界面截图>，重点关注：**
 - 对比<成功案例>中对应步骤的界面，识别界面变化
 - 定位<成功案例>中下一步要操作的目标元素是否可见
 - 识别可能影响操作的界面异常（弹窗、遮挡等）
 - 确认当前界面状态是否符合预期的操作环境"""


def get_reference_action_list() -> str:
    """获取参考任务的动作列表"""
    return """点击屏幕: click(point=<point>x1 y1</point>) # 必须补充点击位置坐标
长按屏幕: long_press(point=<point>x1 y1</point>) # 必须补充长按位置坐标
输入内容: type(content='text_to_input') # 必须补充输入内容
滑动屏幕: scroll(point='<point>x1 y1</point>', direction='down or up or right or left') # 必须补充滑动起点坐标，和direction方向参数
拖动元素: drag(start_point='<point>x1 y1</point>', end_point='<point>x2 y2</point>') # 必须补充拖动起始坐标和结束坐标
等待动作: wait(seconds=wait_seconds) # 必须补充等待秒数
删除内容: delete(content=delete_count) # 必须补充删除文字数量
返回动作: back() # 用于返回上一级页面，或关闭弹窗
失败动作: failed(content='reason') # 必须补充失败原因
成功动作: finished(content='success_message') # 必须补充成功信息"""


def get_reference_exception_handling() -> str:
    """获取异常场景处理策略"""
    return """
**异常场景处理，每处理一次异常后，下次都必须重新调用<执行流程>重新决策**：

1. **弹窗处理**
   - 带倒计时弹窗：调用wait(seconds=倒计时秒数)等待自动消失
   - 普通弹窗：点击'我知道了'、'同意'、'取消'、'X'等关闭按钮
   - 无法关闭的弹窗：调用back()尝试返回

2. **页面异常处理**
   - 空白页/加载页：调用wait(seconds=3)等待，最多等待2次
   - 错误页面：调用back()返回上一级
   - 网络异常页：调用back()返回重试

3. **元素遮挡处理**
   - 悬浮气泡遮挡：通过scroll滑动移开遮挡物
   - 横幅广告遮挡：忽略遮挡，直接操作被遮挡的目标元素

4. **导航修正**
   - 界面与成功案例不符：参考<成功案例>中的导航路径，重新导航到正确界面
   - 找不到目标元素：按<成功案例>的路径重新执行前置步骤
   
**处理原则**：快速处理异常，优先恢复到<成功案例>的执行路径"""


def get_reference_output_example() -> str:
    """获取输出格式要求"""
    return """{{
"current_step_name": "当前执行步骤名称（从成功案例中确定）",
"interface_analysis": "当前界面状态，对比成功案例的差异",
"action_decision": "基于成功案例的操作决策，说明复用哪个步骤的操作",
"action": "具体执行动作，格式严格按照<动作列表>"
}}"""


def get_reference_output_requirement() -> str:
    """获取输出要求"""
    return """使用JSON格式输出内容，严格遵循<输出格式>，保证输出内容与<输出格式>完全一致"""


def get_reference_execution_invoke_prompt() -> str:
    """获取参考任务执行流程调用提示"""
    return """
############ 执行流程 ##########
1. <界面分析>：对比<成功案例>，识别界面变化，判断是否符合<成功案例>的界面分析
2. 直接复用<成功案例>：找到对应步骤，复用操作方式
3. 处理异常：遇到异常按<异常场景>快速解决
4. 执行动作：按<动作列表>格式输出具体操作
5. 严格按照<输出格式>输出结果

**核心思路**：不做复杂分析，直接按成功案例执行，重点处理异常情况
"""


def get_reference_user_invoke_prompt(state: ReferenceTaskState) -> str:
    """获取参考任务用户调用提示"""
    return f"""###################################################
当前是第{state.get("execution_count", 0) + 1}轮执行，必须遵循<执行流程>执行，必须按照<输出格式>输出结果
####################################################
"""


def _build_current_execution_history(history: list) -> str:
    """
    构建当前任务执行历史，兼容多种数据格式

    Args:
        history: 当前任务执行历史列表

    Returns:
        格式化的执行历史文本
    """
    if not history:
        return "当前任务刚开始，暂无执行历史"

    history_content = "以下是当前任务的执行历史：\n"

    # 用于跟踪实际的显示轮次
    display_round = 1

    for i, record in enumerate(history):
        # 获取状态和失败信息
        record_status = record.get("status", "")

        # 跳过初始化记录
        if record.get("action") == "reference_task_initialization":
            continue

        # 显示执行记录
        if record_status != "blocked":
            formatted_record = _format_current_execution_record(record, display_round)
            if formatted_record:
                history_content += formatted_record
                display_round += 1  # 只有成功格式化的记录才增加轮次

    # 如果最终没有任何历史内容，返回默认信息
    if history_content == "以下是当前任务的执行历史：\n":
        return "当前任务刚开始，暂无可显示的执行历史"

    return history_content


def _format_execution_record(record: dict, execution_count: int) -> str:
    """
    格式化单个执行记录为指定的中文格式

    Args:
        record: 执行记录字典
        execution_count: 执行轮次

    Returns:
        格式化的执行记录文本
    """
    # 尝试从parsed_fields获取结构化数据
    parsed_fields = record.get("parsed_fields", {})

    # 如果有parsed_fields，优先使用
    if parsed_fields:
        interface_analysis = parsed_fields.get("interface_analysis", "")
        current_step_name = parsed_fields.get("current_step_name", "")
        action_decision = parsed_fields.get("action_decision", "")
        operation_instruction = parsed_fields.get("instruction", "")
        action = parsed_fields.get("action", "")
    else:
        # 从decision_content或thought字段解析
        decision_content = record.get("decision_content", "")
        thought_content = record.get("thought", "")

        # 优先使用thought字段（API返回格式）
        if thought_content:
            interface_analysis, current_step_name, action_decision, operation_instruction, action = _parse_thought_content(
                thought_content)
        else:
            interface_analysis, current_step_name, action_decision, operation_instruction, action = _parse_decision_content_new(
                decision_content)
    # 如果解析失败，尝试从record的其他字段获取信息
    if not current_step_name:
        current_step_name = record.get("step_name", "")

    if not action:
        action = record.get("action", "")

    if not action_decision:
        action_decision = "执行决策信息待补充"

    if not operation_instruction:
        operation_instruction = "操作指令信息待补充"

    # 如果连步骤名称和动作都没有，则跳过这条记录
    if not current_step_name and not action:
        return ""

    # 构建格式化文本
    formatted_text = f"""**第{execution_count}轮执行**
- 界面分析: {interface_analysis}
- 步骤名称: {current_step_name}
- 执行决策: {action_decision}
- 操作指令: {operation_instruction}
- 执行动作: {action}
"""

    return _escape_template_variables(formatted_text)


def _format_current_execution_record(record: dict, execution_count: int) -> str:
    """
    格式化本次执行记忆记录，包含界面分析字段

    Args:
        record: 执行记录字典
        execution_count: 执行轮次

    Returns:
        格式化的执行记录文本
    """
    # 尝试从parsed_fields获取结构化数据
    parsed_fields = record.get("parsed_fields", {})

    # 如果有parsed_fields，优先使用
    if parsed_fields:
        interface_analysis = parsed_fields.get("interface_analysis", "")
        current_step_name = parsed_fields.get("current_step_name", "")
        action_decision = parsed_fields.get("action_decision", "")
        operation_instruction = parsed_fields.get("instruction", "")
        action = parsed_fields.get("action", "")
    else:
        # 从decision_content或thought字段解析
        decision_content = record.get("decision_content", "")
        thought_content = record.get("thought", "")

        # 优先使用thought字段（API返回格式）
        if thought_content:
            interface_analysis, current_step_name, action_decision, operation_instruction, action = _parse_thought_content(
                thought_content)
        else:
            interface_analysis, current_step_name, action_decision, operation_instruction, action = _parse_decision_content_with_interface(
                decision_content)

    # 如果解析失败，尝试从record的其他字段获取信息
    if not current_step_name:
        current_step_name = record.get("step_name", "")

    if not action:
        action = record.get("action", "")

    # 如果仍然没有关键信息，提供默认值
    if not interface_analysis:
        interface_analysis = ""

    if not action_decision:
        action_decision = ""

    if not operation_instruction:
        operation_instruction = ""

    # 如果连步骤名称和动作都没有，则跳过这条记录
    if not current_step_name and not action:
        return ""

    # 构建格式化文本（包含界面分析）
    formatted_text = f"""**第{execution_count}轮执行**
- 界面分析: {interface_analysis}
- 步骤名称: {current_step_name}
- 执行决策: {action_decision}{"\n- 操作指令: " + operation_instruction if operation_instruction else ""}
- 执行动作: {action}
"""

    return _escape_template_variables(formatted_text)


def _parse_decision_content(decision_content: str) -> tuple:
    """
    从decision_content解析出四个字段

    Args:
        decision_content: 决策内容字符串

    Returns:
        (界面分析, 步骤名称, 执行决策, 执行动作) 的元组
    """
    if not decision_content:
        return "", "", "", ""

    interface_analysis = ""
    current_step_name = ""
    action_decision = ""
    action = ""

    # 尝试解析JSON格式
    try:
        if decision_content.strip().startswith('{'):
            data = json.loads(decision_content)
            interface_analysis = data.get("interface_analysis", "")
            current_step_name = data.get("current_step_name", "")
            action_decision = data.get("action_decision", "")
            action = data.get("action", "")
            return interface_analysis, current_step_name, action_decision, action
    except:
        pass

    # 使用正则表达式解析
    lines = decision_content.split('\n')
    for line in lines:
        line = line.strip()
        if re.match(r'.*界面分析.*[:：]', line):
            interface_analysis = re.sub(r'.*界面分析.*[:：]\s*', '', line)
        elif re.match(r'.*步骤名称.*[:：]', line):
            current_step_name = re.sub(r'.*步骤名称.*[:：]\s*', '', line)
        elif re.match(r'.*执行决策.*[:：]', line):
            action_decision = re.sub(r'.*执行决策.*[:：]\s*', '', line)
        elif re.match(r'.*执行动作.*[:：]', line):
            action = re.sub(r'.*执行动作.*[:：]\s*', '', line)

    return interface_analysis, current_step_name, action_decision, action


def _parse_thought_content(thought_content: str) -> tuple:
    """
    从thought字段解析出五个字段

    Args:
        thought_content: thought字段内容，格式如：
        "界面分析: 界面顶部显示首页横幅...\n执行决策: 根据用例第一步...\n操作指令: 点击页面底部导航栏...\n执行动作: click"

    Returns:
        (界面分析, 步骤名称, 执行决策, 操作指令, 执行动作) 的元组
    """
    if not thought_content:
        return "", "", "", "", ""

    interface_analysis = ""
    current_step_name = ""
    action_decision = ""
    operation_instruction = ""
    action = ""

    # 按行分割内容
    lines = thought_content.split('\n')

    for line in lines:
        line = line.strip()
        if line.startswith("界面分析:") or line.startswith("界面分析："):
            interface_analysis = re.sub(r'^界面分析[:：]\s*', '', line)
        elif line.startswith("执行决策:") or line.startswith("执行决策："):
            action_decision = re.sub(r'^执行决策[:：]\s*', '', line)
        elif line.startswith("操作指令:") or line.startswith("操作指令："):
            operation_instruction = re.sub(r'^操作指令[:：]\s*', '', line)
        elif line.startswith("执行动作:") or line.startswith("执行动作："):
            action = re.sub(r'^执行动作[:：]\s*', '', line)

    return interface_analysis, current_step_name, action_decision, operation_instruction, action


def _parse_decision_content_new(decision_content: str) -> tuple:
    """
    从decision_content解析出四个字段（新格式）

    Args:
        decision_content: 决策内容字符串

    Returns:
        (步骤名称, 执行决策, 操作指令, 执行动作) 的元组
    """
    if not decision_content:
        return "", "", "", ""

    interface_analysis = ""
    current_step_name = ""
    action_decision = ""
    operation_instruction = ""
    action = ""

    # 尝试解析JSON格式
    try:
        if decision_content.strip().startswith('{'):
            data = json.loads(decision_content)
            interface_analysis = data.get("interface_analysis", "")
            current_step_name = data.get("current_step_name", "")
            action_decision = data.get("action_decision", "")
            operation_instruction = data.get("instruction", "")
            action = data.get("action", "")
            return interface_analysis, current_step_name, action_decision, operation_instruction, action
    except:
        pass

    # 使用正则表达式解析
    lines = decision_content.split('\n')
    for line in lines:
        line = line.strip()
        if re.match(r'.*界面分析.*[:：]', line):
            interface_analysis = re.sub(r'.*界面分析.*[:：]\s*', '', line)
        elif re.match(r'.*步骤名称.*[:：]', line):
            current_step_name = re.sub(r'.*步骤名称.*[:：]\s*', '', line)
        elif re.match(r'.*执行决策.*[:：]', line):
            action_decision = re.sub(r'.*执行决策.*[:：]\s*', '', line)
        elif re.match(r'.*操作指令.*[:：]', line):
            operation_instruction = re.sub(r'.*操作指令.*[:：]\s*', '', line)
        elif re.match(r'.*执行动作.*[:：]', line):
            action = re.sub(r'.*执行动作.*[:：]\s*', '', line)

    return interface_analysis, current_step_name, action_decision, operation_instruction, action


def _parse_decision_content_with_interface(decision_content: str) -> tuple:
    """
    从decision_content解析出五个字段（包含界面分析）

    Args:
        decision_content: 决策内容字符串

    Returns:
        (界面分析, 步骤名称, 执行决策, 操作指令, 执行动作) 的元组
    """
    if not decision_content:
        return "", "", "", "", ""

    interface_analysis = ""
    current_step_name = ""
    action_decision = ""
    operation_instruction = ""
    action = ""

    # 尝试解析JSON格式
    try:
        if decision_content.strip().startswith('{'):
            data = json.loads(decision_content)
            interface_analysis = data.get("interface_analysis", "")
            current_step_name = data.get("current_step_name", "")
            action_decision = data.get("action_decision", "")
            operation_instruction = data.get("instruction", "")
            action = data.get("action", "")
            return interface_analysis, current_step_name, action_decision, operation_instruction, action
    except:
        pass

    # 使用正则表达式解析
    lines = decision_content.split('\n')
    for line in lines:
        line = line.strip()
        if re.match(r'.*界面分析.*[:：]', line):
            interface_analysis = re.sub(r'.*界面分析.*[:：]\s*', '', line)
        elif re.match(r'.*步骤名称.*[:：]', line):
            current_step_name = re.sub(r'.*步骤名称.*[:：]\s*', '', line)
        elif re.match(r'.*执行决策.*[:：]', line):
            action_decision = re.sub(r'.*执行决策.*[:：]\s*', '', line)
        elif re.match(r'.*操作指令.*[:：]', line):
            operation_instruction = re.sub(r'.*操作指令.*[:：]\s*', '', line)
        elif re.match(r'.*执行动作.*[:：]', line):
            action = re.sub(r'.*执行动作.*[:：]\s*', '', line)

    return interface_analysis, current_step_name, action_decision, operation_instruction, action


def _escape_template_variables(text: str) -> str:
    """
    转义文本中的模板变量符号，防止LangChain将其识别为变量

    Args:
        text: 原始文本

    Returns:
        转义后的文本
    """
    if not text:
        return text

    # 将单个花括号转义为双花括号
    # 这样LangChain就不会将其识别为模板变量
    return text.replace("{", "{{").replace("}", "}}")


def _build_reference_actions_text(reference_actions: list) -> str:
    """
    构建参考任务动作列表的文本描述

    Args:
        reference_actions: 参考动作列表

    Returns:
        格式化的动作描述文本
    """
    if not reference_actions:
        return ""

    actions_text = ""

    for i, action in enumerate(reference_actions, 1):
        formatted_action = _format_execution_record(action, i)
        if formatted_action:
            actions_text += formatted_action

    return actions_text

def get_simplified_action_decision_prompt() -> str:
    """获取简化的动作决策提示"""
    return """
1. **确定当前步骤**
   - 对比<执行记忆>和<成功案例>，确定当前执行到第几步
   - 从<成功案例>中找到对应步骤的操作内容

2. **直接复用成功案例**
   - 优先使用<成功案例>中相同步骤的决策与操作
   - 如果界面布局有变化，调整坐标但保持操作逻辑一致
   - 如果目标元素不可见，按<成功案例>的方式进行滑动或导航

3. **异常情况处理**
   - 遇到弹窗、错误页等异常，按<异常场景>快速处理
   - 处理完异常后，继续按<成功案例>执行

4. **执行动作**
   - 从<动作列表>中选择对应的动作格式
   - 补充具体的坐标和参数
   - 每轮只执行一个动作

5. **完成判断**
   - 所有步骤执行完毕且达到期望结果：调用finished()
   - 遇到无法解决的问题：调用failed()
   
**核心原则**：不需要复杂分析，直接按<成功案例>操作，重点处理异常"""
